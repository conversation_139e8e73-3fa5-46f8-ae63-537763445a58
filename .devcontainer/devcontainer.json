{"name": "Bun with GLIBC Support", "image": "ubuntu:22.04", "features": {"ghcr.io/devcontainers/features/common-utils:2": {"installZsh": true, "configureZshAsDefaultShell": true, "installOhMyZsh": true, "upgradePackages": true, "username": "vscode", "userUid": "automatic", "userGid": "automatic"}, "ghcr.io/devcontainers/features/node:1": {"version": "20"}}, "postCreateCommand": "bash -c 'curl -fsSL https://bun.sh/install | bash && echo \"export PATH=\\$HOME/.bun/bin:\\$PATH\" >> ~/.bashrc && sudo apt-get update && sudo apt-get install -y libc6-dev build-essential git'", "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint"]}}, "remoteEnv": {"PATH": "${containerEnv:PATH}:/home/<USER>/.bun/bin"}, "remoteUser": "vscode"}