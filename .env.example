# 1--- section: setup inside your `.env` file
BETTER_AUTH_SECRET="<bunx uuid>"
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/intern3_db"
VITE_CONVEX_URL=http://127.0.0.1:3210
VITE_CONVEX_API_URL=http://127.0.0.1:3210/http
VITE_POSTHOG_HOST=https://eu.i.posthog.com
VITE_POSTHOG_KEY=phc_***

GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
TWITCH_CLIENT_ID=your_twitch_client_id
TWITCH_CLIENT_SECRET=your_twitch_client_secret
# 1---


# 2--- section: Add these in convex dashboard AND `.env` file
VITE_BETTER_AUTH_URL="http://localhost:3000"
UPSTASH_REDIS_REST_URL="<upstash instance, NOT local redis>"
UPSTASH_REDIS_REST_TOKEN="<upstash key>"
ENCRYPTION_KEY="<bunx uuid>"
# 2--- end section


# 3--- section: Add these in convex dashboard ONLY
OPENAI_API_KEY="sk-meow-****"
GOOGLE_GENERATIVE_AI_API_KEY="AI***"
R2_FORCE_PATH_STYLE=true
R2_BUCKET=intern3-user-files
R2_ENDPOINT="http://localhost:9000"
R2_ACCESS_KEY_ID="minioadmin"
R2_SECRET_ACCESS_KEY="minioadmin"
# 3---


# 4--- email: add EITHER the RESEND or SES config in `.env`

EMAIL_PROVIDER=<resend|ses|local-only-mock>
EMAIL_FROM=<EMAIL>
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx
AWS_REGION=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

# Note: `.env.local` is generated by convex. Do not edit the file.
# When it is said to use `.env`, you must specifically use that file, NOT `.env.local`
# Make sure to run `bun auth:migrate` when setting up!