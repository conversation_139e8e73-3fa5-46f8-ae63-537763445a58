# Intern3 Chat

A modern, feature-rich AI chat application built by interns, for interns. It's the second-best chat app in the world, after T3 chat. It's fast, has lots of features, and is open-source.

![intern3.chat](public/opengraph.jpg)

🔗 **Live demo →** [https://intern3.chat](https://intern3.chat)

📚 **Documentation →** [https://docs.intern3.chat](https://docs.intern3.chat)

## Core features

- **Multi-model support** with Gemini, OpenAI, Claude, and more.
- **BYOK API key system**: Native Provider BYOK → OpenRouter BYOK → In-house credits.
- **In-house credits** for select models (no API key required)
- **Custom AI prompt** configuration
- **Web Search** integration with Brave Search and Firecrawl
- **Image Generation** using fal.ai models and GPT-Image-1
- **HTML/Mermaid/React** artifacts preview
- **Native Voice input** in input box using Groq
- **HTTP/SSE MCP Support** for Model Context Protocol
- **Supermemory API** integration for persistent memories
- **File Attachments** - Upload code, text files, PDFs, and images

### Experience features
- **Resumable streams** for reliable message delivery
- **Edit & Regenerate** any message
- **Copy messages** with one click
- **Thread management** with sidebar navigation
- **Folder organization** for chat threads
- **CMD+K search bar** for quick chat navigation

### Customization & UI
- **Beautiful, modern interface** with multiple theming options
- **Normal/Wide chat view** options
- **Responsive design** for all devices

## Quick start

### Option 1: Use Hosted Version
Simply visit [https://intern3.chat](https://intern3.chat) and sign up for an account.

### Option 2: Self-Hosted Deployment
Follow our [Vercel deployment guide](https://docs.intern3.chat/deployment/vercel) to set up your own instance.



## Footnotes
- [1]: Some models are available with in-house credits, so API keys aren't always required! But not all, so we don't go broke (looking at you, `o3`, `claude-sonnet-4` and `claude-opus-4`'s of the world...)
- [2]: You might have to add in the model IDs you would like to use yourself.
