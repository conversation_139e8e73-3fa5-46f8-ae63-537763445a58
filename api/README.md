# API Server

A Rust-based API server using Axum and PostgreSQL.

## Setup

1. Install Rust and Cargo
2. Install PostgreSQL
3. Copy `.env.example` to `.env` and update the database URL
4. Create the database:
   ```bash
   createdb intern3_chat
   ```
5. Run the server:
   ```bash
   cargo run
   ```

## API Endpoints

- `GET /` - Health check
- `GET /api/health` - Health check
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user

## Database

The server uses PostgreSQL with SQLx for database operations. Migrations are automatically run on startup.

## Development

To run in development mode with auto-reload:
```bash
cargo install cargo-watch
cargo watch -x run
```
