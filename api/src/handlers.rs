use axum::{
    extract::State,
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use chrono::Utc;
use uuid::Uuid;

use crate::{
    models::{User, CreateUserRequest, CreateUserResponse, HealthResponse},
    AppState,
};

pub async fn health_check() -> <PERSON><PERSON><HealthResponse> {
    Json(HealthResponse {
        status: "ok".to_string(),
        timestamp: Utc::now(),
    })
}

pub async fn get_users(State(state): State<AppState>) -> Result<Json<Vec<User>>, StatusCode> {
    let users = sqlx::query_as::<_, User>("SELECT * FROM users ORDER BY created_at DESC")
        .fetch_all(&state.db.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to fetch users: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    Ok(<PERSON><PERSON>(users))
}

pub async fn create_user(
    State(state): State<AppState>,
    J<PERSON>(payload): Json<CreateUserRequest>,
) -> Result<Json<CreateUserResponse>, StatusCode> {
    let user_id = Uuid::new_v4();
    let now = Utc::now();

    let user = sqlx::query_as::<_, User>(
        r#"
        INSERT INTO users (id, email, name, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
        "#,
    )
    .bind(user_id)
    .bind(&payload.email)
    .bind(&payload.name)
    .bind(now)
    .bind(now)
    .fetch_one(&state.db.pool)
    .await
    .map_err(|e| {
        tracing::error!("Failed to create user: {}", e);
        StatusCode::INTERNAL_SERVER_ERROR
    })?;

    Ok(Json(CreateUserResponse {
        id: user.id,
        email: user.email,
        name: user.name,
        created_at: user.created_at,
    }))
}
