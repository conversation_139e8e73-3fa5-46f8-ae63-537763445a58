{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "main"}, "files": {"ignore": ["./components/ui/**", "./migrations/**", "routeTree.gen.ts", "./convex/_generated/**", "./src/lib/pdfjs_dist/**"]}, "formatter": {"indentStyle": "space", "indentWidth": 4, "lineWidth": 100}, "linter": {"rules": {"a11y": {"noSvgWithoutTitle": "off", "useGenericFontNames": "off"}, "suspicious": {"noArrayIndexKey": "off", "noDoubleEquals": {"fix": "safe", "level": "warn", "options": {}}, "noExplicitAny": "warn"}, "style": {"noNonNullAssertion": "off", "useSelfClosingElements": {"fix": "safe", "level": "warn"}, "useTemplate": {"fix": "safe", "level": "warn"}}, "correctness": {"noUnusedImports": "info", "useExhaustiveDependencies": "warn"}, "nursery": {"useSortedClasses": {"fix": "safe", "level": "info", "options": {"functions": ["cn"]}}}, "complexity": {"noForEach": "off"}}}, "javascript": {"formatter": {"semicolons": "asNeeded", "trailingCommas": "none"}}}