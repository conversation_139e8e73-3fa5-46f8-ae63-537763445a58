{"name": "intern3-chat", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite dev --port 3000", "build": "vite build", "start": "node .output/server/index.mjs", "lint": "biome check --write", "check-types": "tsc --noEmit", "postinstall": "patch-package && cd node_modules/@convex-dev/r2 && tsc --project ./esm.json && echo '{\"type\": \"module\"}' > dist/esm/package.json", "auth:generate": "npx @better-auth/cli generate --output src/database/auth-schema.ts", "auth:migrate": "npx drizzle-kit migrate", "auth:push": "npx drizzle-kit push", "prepare": "husky"}, "keywords": ["better-auth", "tanstack", "vite", "react"], "author": "intern3chat", "license": "MIT", "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/fal": "^0.1.12", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/react": "^1.2.12", "@ai-sdk/ui-utils": "^1.2.11", "@aws-sdk/client-ses": "^3.830.0", "@codesandbox/sandpack-react": "^2.20.0", "@convex-dev/aggregate": "^0.1.21", "@convex-dev/migrations": "^0.2.9", "@convex-dev/r2": "git://github.com/f1shy-dev/r2", "@convex-dev/react-query": "0.0.0-alpha.8", "@daveyplate/better-auth-tanstack": "^1.3.4", "@daveyplate/better-auth-ui": "^1.5.2", "@hookform/resolvers": "^4.1.3", "@modelcontextprotocol/sdk": "^1.12.3", "@openrouter/ai-sdk-provider": "^0.7.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.1.0", "@react-email/render": "^1.1.2", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-router": "^1.121.0-alpha.27", "@tanstack/react-router-devtools": "^1.121.0-alpha.27", "@tanstack/react-router-with-query": "^1.120.18", "@tanstack/react-start": "^1.121.0-alpha.27", "@types/ua-parser-js": "^0.7.39", "@types/ungap__structured-clone": "^1.2.0", "@ungap/structured-clone": "^1.3.0", "@upstash/redis": "^1.35.0", "@zanreal/search": "^1.0.0", "ai": "^4.3.16", "babel-plugin-react-compiler": "^19.1.0-rc.2", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.24.8", "convex-helpers": "^0.1.94", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "embla-carousel-react": "^8.6.0", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "fast-deep-equal": "^3.1.3", "hast-util-sanitize": "^5.0.2", "input-otp": "^1.4.2", "install": "^0.13.0", "katex": "^0.16.22", "lucide-react": "^0.503.0", "marked": "^15.0.12", "mermaid": "^11.6.0", "micromark-extension-llm-math": "^3.1.0", "motion": "^12.18.1", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "patch-package": "^8.0.0", "pg": "^8.14.1", "posthog-js": "^1.254.0", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.2", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.15.3", "rehype-katex": "^7.0.1", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resend": "^4.6.0", "resumable-stream": "^2.2.0", "shiki": "^3.6.0", "sonner": "^2.0.5", "supermemory": "^3.0.0-alpha.18", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tailwindcss-safe-area": "^0.6.0", "ts-dedent": "^2.2.0", "ua-parser-js": "^2.0.3", "unpdf": "^1.0.6", "use-stick-to-bottom": "^1.1.1", "vaul": "^1.1.2", "vite": "^6.3.5", "zod": "^3.25.63", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "drizzle-kit": "^0.31.0", "esbuild": "^0.25.5", "husky": "^9.1.7", "lint-staged": "^16.1.1", "tsx": "^4.19.3", "turbo": "^2.5.0", "tw-animate-css": "^1.2.8", "typescript": "^5.8.3", "vite-plugin-mkcert": "^1.17.8", "vite-bundle-analyzer": "^0.22.3", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}, "packageManager": "pnpm@10.6.4", "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "esbuild", "sharp"]}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["biome check --write --files-ignore-unknown=true --no-errors-on-unmatched"]}}