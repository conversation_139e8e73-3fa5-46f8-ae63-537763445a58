diff --git a/node_modules/resumable-stream/dist/redis.js b/node_modules/resumable-stream/dist/redis.js
index 5d7fb67..e2539d9 100644
--- a/node_modules/resumable-stream/dist/redis.js
+++ b/node_modules/resumable-stream/dist/redis.js
@@ -1,22 +1,22 @@
 "use strict";
-var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
+var __createBinding = (this && this.__createBinding) || (Object.create ? (function (o, m, k, k2) {
     if (k2 === undefined) k2 = k;
     var desc = Object.getOwnPropertyDescriptor(m, k);
     if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
-      desc = { enumerable: true, get: function() { return m[k]; } };
+        desc = { enumerable: true, get: function () { return m[k]; } };
     }
     Object.defineProperty(o, k2, desc);
-}) : (function(o, m, k, k2) {
+}) : (function (o, m, k, k2) {
     if (k2 === undefined) k2 = k;
     o[k2] = m[k];
 }));
-var __exportStar = (this && this.__exportStar) || function(m, exports) {
+var __exportStar = (this && this.__exportStar) || function (m, exports) {
     for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
 };
 Object.defineProperty(exports, "__esModule", { value: true });
 exports.createResumableStreamContext = exports.resumeStream = void 0;
 const get_redis_url_1 = require("./get-redis-url");
-const redis_1 = require("redis");
+const redis_1 = require("@upstash/redis");
 const runtime_1 = require("./runtime");
 __exportStar(require("./types"), exports);
 var runtime_2 = require("./runtime");
