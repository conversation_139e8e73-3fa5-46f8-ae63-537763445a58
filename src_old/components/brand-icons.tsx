import { cn } from "@/lib/utils"
import type { SVGProps } from "react"

export function GoogleIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="0.98em"
            height="1em"
            viewBox="0 0 256 262"
            {...props}
        >
            {/* Icon from SVG Logos by <PERSON> - https://raw.githubusercontent.com/gilbarbara/logos/master/LICENSE.txt */}
            <path
                fill="#4285F4"
                d="M255.878 133.451c0-10.734-.871-18.567-2.756-26.69H130.55v48.448h71.947c-1.45 12.04-9.283 30.172-26.69 42.356l-.244 1.622l38.755 30.023l2.685.268c24.659-22.774 38.875-56.282 38.875-96.027"
            />
            <path
                fill="#34A853"
                d="M130.55 261.1c35.248 0 64.839-11.605 86.453-31.622l-41.196-31.913c-11.024 7.688-25.82 13.055-45.257 13.055c-34.523 0-63.824-22.773-74.269-54.25l-1.531.13l-40.298 31.187l-.527 1.465C35.393 231.798 79.49 261.1 130.55 261.1"
            />
            <path
                fill="#FBBC05"
                d="M56.281 156.37c-2.756-8.123-4.351-16.827-4.351-25.82c0-8.994 1.595-17.697 4.206-25.82l-.073-1.73L15.26 71.312l-1.335.635C5.077 89.644 0 109.517 0 130.55s5.077 40.905 13.925 58.602z"
            />
            <path
                fill="#EB4335"
                d="M130.55 50.479c24.514 0 41.05 10.589 50.479 19.438l36.844-35.974C195.245 12.91 165.798 0 130.55 0C79.49 0 35.393 29.301 13.925 71.947l42.211 32.783c10.59-31.477 39.891-54.251 74.414-54.251"
            />
        </svg>
    )
}

export function GithubIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="1em"
            height="1em"
            viewBox="0 0 24 24"
            {...props}
        >
            {/* Icon from Material Design Icons by Pictogrammers - https://github.com/Templarian/MaterialDesign/blob/master/LICENSE */}
            <path
                fill="currentColor"
                d="M12 2A10 10 0 0 0 2 12c0 4.42 2.87 8.17 6.84 9.5c.5.08.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34c-.46-1.16-1.11-1.47-1.11-1.47c-.91-.62.07-.6.07-.6c1 .07 1.53 1.03 1.53 1.03c.87 1.52 2.34 1.07 2.91.83c.09-.65.35-1.09.63-1.34c-2.22-.25-4.55-1.11-4.55-4.92c0-1.11.38-2 1.03-2.71c-.1-.25-.45-1.29.1-2.64c0 0 .84-.27 2.75 1.02c.79-.22 1.65-.33 2.5-.33s1.71.11 2.5.33c1.91-1.29 2.75-1.02 2.75-1.02c.55 1.35.2 2.39.1 2.64c.65.71 1.03 1.6 1.03 2.71c0 3.82-2.34 4.66-4.57 4.91c.36.31.69.92.69 1.85V21c0 .27.16.59.67.5C19.14 20.16 22 16.42 22 12A10 10 0 0 0 12 2"
            />
        </svg>
    )
}

export function TwitchIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="1em"
            height="1em"
            viewBox="0 0 24 24"
            {...props}
        >
            {/* Icon from Material Design Icons by Pictogrammers - https://github.com/Templarian/MaterialDesign/blob/master/LICENSE */}
            <path
                fill="currentColor"
                d="M11.64 5.93h1.43v4.28h-1.43m3.93-4.28H17v4.28h-1.43M7 2L3.43 5.57v12.86h4.28V22l3.58-3.57h2.85L20.57 12V2m-1.43 9.29l-2.85 2.85h-2.86l-2.5 2.5v-2.5H7.71V3.43h11.43Z"
            />
        </svg>
    )
}

export function BlackForestLabsIcon({ className, ...rest }: React.SVGProps<SVGSVGElement>) {
    return (
        <svg
            fill="currentColor"
            fillRule="evenodd"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
            className={cn("line-height-1 size-4 flex-none", className)}
        >
            <title>Flux</title>
            <path d="M0 20.683L12.01 2.5 24 20.683h-2.233L12.009 5.878 3.471 18.806h12.122l1.239 1.877H0z" />
            <path d="M8.069 16.724l2.073-3.115 2.074 3.115H8.069zM18.24 20.683l-5.668-8.707h2.177l5.686 8.707h-2.196zM19.74 11.676l2.13-3.19 2.13 3.19h-4.26z" />
        </svg>
    )
}

export function StabilityIcon({ className, ...rest }: React.SVGProps<SVGSVGElement>) {
    return (
        <svg
            className={cn("line-height-1 size-4 flex-none", className)}
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
        >
            <title>Stability</title>
            <g fill="none" fillRule="nonzero">
                <path
                    d="M7.223 21c4.252 0 7.018-2.22 7.018-5.56 0-2.59-1.682-4.236-4.69-4.918l-1.93-.571c-1.694-.375-2.683-.825-2.45-1.975.194-.957.773-1.497 2.122-1.497 4.285 0 5.873 1.497 5.873 1.497v-3.6S11.62 3 7.293 3C3.213 3 1 5.07 1 8.273c0 2.59 1.534 4.097 4.645 4.812l.334.083c.473.144 1.112.335 1.916.572 1.59.375 1.999.773 1.999 1.966 0 1.09-1.15 1.71-2.67 1.71C2.841 17.416 1 15.231 1 15.231v3.989S2.152 21 7.223 21z"
                    fill="currentColor"
                />
                <path
                    d="M20.374 20.73c1.505 0 2.626-1.073 2.626-2.526 0-1.484-1.089-2.526-2.626-2.526-1.505 0-2.594 1.042-2.594 2.526 0 1.484 1.089 2.526 2.594 2.526z"
                    fill="currentColor"
                />
            </g>
        </svg>
    )
}

export function BraveIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2770 2770" {...props}>
            <linearGradient id="a" y1="51%" y2="51%">
                <stop offset=".4" stopColor="#f50" />
                <stop offset=".6" stopColor="#ff2000" />
            </linearGradient>
            <linearGradient id="b" x1="2%" y1="51%" y2="51%">
                <stop offset="0" stopColor="#ff452a" />
                <stop offset="1" stopColor="#ff2000" />
            </linearGradient>
            <path
                fill="url(#a)"
                d="M2395 723l60-147-170-176c-92-92-288-38-288-38l-222-252H992L769 363s-196-53-288 37L311 575l60 147-75 218 250 953c52 204 87 283 234 387l457 310c44 27 98 74 147 74s103-47 147-74l457-310c147-104 182-183 234-387l250-953z"
            />
            <path
                fill="#fff"
                d="M1935 524s287 347 287 420c0 75-36 94-72 133l-215 230c-20 20-63 54-38 113 25 60 60 134 20 210-40 77-110 128-155 120a820 820 0 01-190-90c-38-25-160-126-160-165s126-110 150-124c23-16 130-78 132-102s2-30-30-90-88-140-80-192c10-52 100-80 167-105l207-78c16-8 12-15-36-20-48-4-183-22-244-5s-163 43-173 57c-8 14-16 14-7 62l58 315c4 40 12 67-30 77-44 10-117 27-142 27s-99-17-142-27-35-37-30-77c4-40 48-268 57-315 10-48 1-48-7-62-10-14-113-40-174-57-60-17-196 1-244 6-48 4-52 10-36 20l207 77c66 25 158 53 167 105 10 53-47 132-80 192s-32 66-30 90 110 86 132 102c24 15 150 85 150 124s-119 140-159 165a820 820 0 01-190 90c-45 8-115-43-156-120-40-76-4-150 20-210 25-60-17-92-38-113l-215-230c-35-37-71-57-71-131s287-420 287-420l273 44c32 0 103-27 168-50 65-20 110-22 110-22s44 0 110 22s136 50 168 50c33 0 275-47 275-47zm-215 1328c18 10 7 32-10 44l254 198c-20 20-52 50-73 50s-52-30-73-50a13200 13200 0 00-255-198c-16-12-27-33-10-44l150-80a870 870 0 01188-73c15 0 110 34 187 73l150 80z"
            />
            <path
                fill="url(#b)"
                d="M1999 363l-224-253H992L769 363s-196-53-288 37c0 0 260-23 350 123l276 47c32 0 103-27 168-50 65-20 110-22 110-22s44 0 110 22s136 50 168 50c33 0 275-47 275-47 90-146 350-123 350-123-92-92-288-38-288-38"
            />
        </svg>
    )
}

export function ClaudeIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="256"
            height="257"
            preserveAspectRatio="xMidYMid"
            viewBox="0 0 256 257"
            {...props}
        >
            <path
                fill="currentColor"
                d="m50.228 170.321 50.357-28.257.843-2.463-.843-1.361h-2.462l-8.426-.518-28.775-.778-24.952-1.037-24.175-1.296-6.092-1.297L0 125.796l.583-3.759 5.12-3.434 7.324.648 16.202 1.101 24.304 1.685 17.629 1.037 26.118 2.722h4.148l.583-1.685-1.426-1.037-1.101-1.037-25.147-17.045-27.22-18.017-14.258-10.37-7.713-5.25-3.888-4.925-1.685-10.758 7-7.713 9.397.649 2.398.648 9.527 7.323 20.35 15.75L94.817 91.9l3.889 3.24 1.555-1.102.195-.777-1.75-2.917-14.453-26.118-15.425-26.572-6.87-11.018-1.814-6.61c-.648-2.723-1.102-4.991-1.102-7.778l7.972-10.823L71.42 0 82.05 1.426l4.472 3.888 6.61 15.101 10.694 23.786 16.591 32.34 4.861 9.592 2.592 8.879.973 2.722h1.685v-1.556l1.36-18.211 2.528-22.36 2.463-28.776.843-8.1 4.018-9.722 7.971-5.25 6.222 2.981 5.12 7.324-.713 4.73-3.046 19.768-5.962 30.98-3.889 20.739h2.268l2.593-2.593 10.499-13.934 17.628-22.036 7.778-8.749 9.073-9.657 5.833-4.601h11.018l8.1 12.055-3.628 12.443-11.342 14.388-9.398 12.184-13.48 18.147-8.426 14.518.778 1.166 2.01-.194 30.46-6.481 16.462-2.982 19.637-3.37 8.88 4.148.971 4.213-3.5 8.62-20.998 5.184-24.628 4.926-36.682 8.685-.454.324.519.648 16.526 1.555 7.065.389h17.304l32.21 2.398 8.426 5.574 5.055 6.805-.843 5.184-12.962 6.611-17.498-4.148-40.83-9.721-14-3.5h-1.944v1.167l11.666 11.406 21.387 19.314 26.767 24.887 1.36 6.157-3.434 4.86-3.63-.518-23.526-17.693-9.073-7.972-20.545-17.304h-1.36v1.814l4.73 6.935 25.017 37.59 1.296 11.536-1.814 3.76-6.481 2.268-7.13-1.297-14.647-20.544-15.1-23.138-12.185-20.739-1.49.843-7.194 77.448-3.37 3.953-7.778 2.981-6.48-4.925-3.436-7.972 3.435-15.749 4.148-20.544 3.37-16.333 3.046-20.285 1.815-6.74-.13-.454-1.49.194-15.295 20.999-23.267 31.433-18.406 19.702-4.407 1.75-7.648-3.954.713-7.064 4.277-6.286 25.47-32.405 15.36-20.092 9.917-11.6-.065-1.686h-.583L44.07 198.125l-12.055 1.555-5.185-4.86.648-7.972 2.463-2.593 20.35-13.999-.064.065Z"
            />
        </svg>
    )
}

export function FalAIIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="24"
            height="24"
            viewBox="0 0 1855 1855"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1181.65 78C1212.05 78 1236.42 101.947 1239.32 131.261C1265.25 392.744 1480.07 600.836 1750.02 625.948C1780.28 628.764 1805 652.366 1805 681.816V1174.18C1805 1203.63 1780.28 1227.24 1750.02 1230.05C1480.07 1255.16 1265.25 1463.26 1239.32 1724.74C1236.42 1754.05 1212.05 1778 1181.65 1778H673.354C642.951 1778 618.585 1754.05 615.678 1724.74C589.754 1463.26 374.927 1255.16 104.984 1230.05C74.7212 1227.24 50 1203.63 50 1174.18V681.816C50 652.366 74.7213 628.764 104.984 625.948C374.927 600.836 589.754 392.744 615.678 131.261C618.585 101.946 642.951 78 673.353 78H1181.65ZM402.377 926.561C402.377 1209.41 638.826 1438.71 930.501 1438.71C1222.18 1438.71 1458.63 1209.41 1458.63 926.561C1458.63 643.709 1222.18 414.412 930.501 414.412C638.826 414.412 402.377 643.709 402.377 926.561Z"
                fill="black"
            />
        </svg>
    )
}

export function GeminiIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            height="1em"
            style={{ flex: "none", lineHeight: 1 }}
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <title>Gemini</title>
            <path
                d="M12 24A14.304 14.304 0 000 12 14.304 14.304 0 0012 0a14.305 14.305 0 0012 12 14.305 14.305 0 00-12 12"
                fill="currentColor"
                fillRule="nonzero"
            />
        </svg>
    )
}

export function MCPIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            fill="currentColor"
            fillRule="evenodd"
            height="1em"
            style={{ flex: "none", lineHeight: 1 }}
            viewBox="0 0 24 24"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <title>ModelContextProtocol</title>
            <path d="M15.688 2.343a2.588 2.588 0 00-3.61 0l-9.626 9.44a.863.863 0 01-1.203 0 .823.823 0 010-1.18l9.626-9.44a4.313 4.313 0 016.016 0 4.116 4.116 0 011.204 3.54 4.3 4.3 0 013.609 1.18l.05.05a4.115 4.115 0 010 5.9l-8.706 8.537a.274.274 0 000 .393l1.788 1.754a.823.823 0 010 1.18.863.863 0 01-1.203 0l-1.788-1.753a1.92 1.92 0 010-2.754l8.706-8.538a2.47 2.47 0 000-3.54l-.05-.049a2.588 2.588 0 00-3.607-.003l-7.172 7.034-.002.002-.098.097a.863.863 0 01-1.204 0 .823.823 0 010-1.18l7.273-7.133a2.47 2.47 0 00-.003-3.537z" />
            <path d="M14.485 4.703a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a4.115 4.115 0 000 5.9 4.314 4.314 0 006.016 0l7.12-6.982a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a2.588 2.588 0 01-3.61 0 2.47 2.47 0 010-3.54l7.12-6.982z" />
        </svg>
    )
}

export function OpenAIIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="256"
            height="260"
            preserveAspectRatio="xMidYMid"
            viewBox="0 0 256 260"
            {...props}
        >
            <path
                fill="currentColor"
                d="M239.184 106.203a64.716 64.716 0 0 0-5.576-53.103C219.452 28.459 191 15.784 163.213 21.74A65.586 65.586 0 0 0 52.096 45.22a64.716 64.716 0 0 0-43.23 31.36c-14.31 24.602-11.061 55.634 8.033 76.74a64.665 64.665 0 0 0 5.525 53.102c14.174 24.65 42.644 37.324 70.446 31.36a64.72 64.72 0 0 0 48.754 21.744c28.481.025 53.714-18.361 62.414-45.481a64.767 64.767 0 0 0 43.229-31.36c14.137-24.558 10.875-55.423-8.083-76.483Zm-97.56 136.338a48.397 48.397 0 0 1-31.105-11.255l1.535-.87 51.67-29.825a8.595 8.595 0 0 0 4.247-7.367v-72.85l21.845 12.636c.218.111.37.32.409.563v60.367c-.056 26.818-21.783 48.545-48.601 48.601Zm-104.466-44.61a48.345 48.345 0 0 1-5.781-32.589l1.534.921 51.722 29.826a8.339 8.339 0 0 0 8.441 0l63.181-36.425v25.221a.87.87 0 0 1-.358.665l-52.335 30.184c-23.257 13.398-52.97 5.431-66.404-17.803ZM23.549 85.38a48.499 48.499 0 0 1 25.58-21.333v61.39a8.288 8.288 0 0 0 4.195 7.316l62.874 36.272-21.845 12.636a.819.819 0 0 1-.767 0L41.353 151.53c-23.211-13.454-31.171-43.144-17.804-66.405v.256Zm179.466 41.695-63.08-36.63L161.73 77.86a.819.819 0 0 1 .768 0l52.233 30.184a48.6 48.6 0 0 1-7.316 87.635v-61.391a8.544 8.544 0 0 0-4.4-7.213Zm21.742-32.69-1.535-.922-51.619-30.081a8.39 8.39 0 0 0-8.492 0L99.98 99.808V74.587a.716.716 0 0 1 .307-.665l52.233-30.133a48.652 48.652 0 0 1 72.236 50.391v.205ZM88.061 139.097l-21.845-12.585a.87.87 0 0 1-.41-.614V65.685a48.652 48.652 0 0 1 79.757-37.346l-1.535.87-51.67 29.825a8.595 8.595 0 0 0-4.246 7.367l-.051 72.697Zm11.868-25.58 28.138-16.217 28.188 16.218v32.434l-28.086 16.218-28.188-16.218-.052-32.434Z"
            />
        </svg>
    )
}

export function OpenRouterIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="100%"
            height="100%"
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
            className="size-4"
            fill="currentColor"
            stroke="currentColor"
            aria-label="Logo"
            {...props}
        >
            <g clipPath="url(#clip0_205_3)">
                <path
                    d="M3 248.945C18 248.945 76 236 106 219C136 202 136 202 198 158C276.497 102.293 332 120.945 423 120.945"
                    strokeWidth="90"
                />
                <path d="M511 121.5L357.25 210.268L357.25 32.7324L511 121.5Z" />
                <path
                    d="M0 249C15 249 73 261.945 103 278.945C133 295.945 133 295.945 195 339.945C273.497 395.652 329 377 420 377"
                    strokeWidth="90"
                />
                <path d="M508 376.445L354.25 287.678L354.25 465.213L508 376.445Z" />
            </g>
            <title style={{ display: "none" }}>OpenRouter</title>
            <defs>
                <clipPath id="clip0_205_3">
                    <rect width="512" height="512" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export function SerperDevIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            viewBox="0 0 75 75"
            xmlns="http://www.w3.org/2000/svg"
            className="css-11i3tf4"
            {...props}
        >
            <path
                d="M35.6992 17.5264C37.571 17.5264 39.3369 17.8438 40.9971 18.4785C42.6735 19.1133 44.1383 19.9922 45.3916 21.1152C46.6449 22.2383 47.597 23.5404 48.248 25.0215C48.3945 25.347 48.4678 25.6807 48.4678 26.0225C48.4678 26.6898 48.2236 27.2676 47.7354 27.7559C47.2633 28.2279 46.6937 28.4639 46.0264 28.4639C45.5869 28.4639 45.1475 28.3255 44.708 28.0488C44.2686 27.7559 43.9593 27.4059 43.7803 26.999C43.1618 25.5993 42.1283 24.4844 40.6797 23.6543C39.2474 22.8242 37.5872 22.4092 35.6992 22.4092C34.7715 22.4092 33.7868 22.5231 32.7451 22.751C31.7197 22.9788 30.7513 23.3288 29.8398 23.8008C28.9284 24.2565 28.1878 24.8343 27.6182 25.5342C27.0485 26.234 26.7637 27.0479 26.7637 27.9756C26.7637 28.7243 27.0078 29.3753 27.4961 29.9287C27.9844 30.4658 28.5296 30.889 29.1318 31.1982C30.5479 31.8981 32.1104 32.3864 33.8193 32.6631C35.5446 32.9398 37.2861 33.2083 39.0439 33.4688C40.8018 33.7292 42.4375 34.1849 43.9512 34.8359C44.9115 35.2428 45.8311 35.8044 46.71 36.5205C47.5889 37.2367 48.305 38.1074 48.8584 39.1328C49.4118 40.1582 49.6885 41.3626 49.6885 42.7461C49.6885 44.5365 49.2572 46.1071 48.3945 47.458C47.5319 48.8089 46.4007 49.932 45.001 50.8271C43.6012 51.7223 42.0794 52.3978 40.4355 52.8535C38.8079 53.293 37.2129 53.5127 35.6504 53.5127C33.5345 53.5127 31.5488 53.179 29.6934 52.5117C27.8379 51.8281 26.2184 50.8678 24.835 49.6309C23.4515 48.3776 22.3936 46.9128 21.6611 45.2363C21.5146 44.9108 21.4414 44.5853 21.4414 44.2598C21.4414 43.5924 21.6774 43.0228 22.1494 42.5508C22.6377 42.0625 23.2155 41.8184 23.8828 41.8184C24.3385 41.8184 24.7861 41.9648 25.2256 42.2578C25.665 42.5345 25.9661 42.8844 26.1289 43.3076C26.8451 44.9515 28.0576 46.2536 29.7666 47.2139C31.4756 48.1579 33.4368 48.6299 35.6504 48.6299C37.0339 48.6299 38.4255 48.4264 39.8252 48.0195C41.2249 47.5964 42.3968 46.9535 43.3408 46.0908C44.3011 45.2119 44.7812 44.097 44.7812 42.7461C44.7812 41.8835 44.4883 41.1755 43.9023 40.6221C43.3327 40.0524 42.7061 39.6211 42.0225 39.3281C40.4762 38.6608 38.8242 38.2051 37.0664 37.9609C35.3086 37.7005 33.5589 37.432 31.8174 37.1553C30.0758 36.8623 28.4482 36.3333 26.9346 35.5684C25.6488 34.9173 24.4769 33.9733 23.4189 32.7363C22.3773 31.4831 21.8564 29.8962 21.8564 27.9756C21.8564 26.2829 22.2633 24.7936 23.0771 23.5078C23.9072 22.2057 24.9977 21.1152 26.3486 20.2363C27.7158 19.3411 29.2132 18.6657 30.8408 18.21C32.4684 17.7542 34.0879 17.5264 35.6992 17.5264Z"
                fill="#90CDF4"
            />
            <circle cx="35" cy="35" r="32" stroke="#90CDF4" strokeWidth="6" fill="transparent" />
        </svg>
    )
}

export function SupermemoryIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="37"
            height="30"
            viewBox="0 0 37 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M36.6817 11.8341H23.0683V0.00134277H18.6699V12.8401C18.6699 14.2037 19.2082 15.5133 20.165 16.4782L31.2807 27.6883L34.3907 24.5519L26.1808 16.2724H36.6842V11.8367L36.6817 11.8341Z"
                fill="currentColor"
            />
            <path
                d="M2.29357 5.45069L10.5035 13.7303H0V18.166H13.6134V29.9987H18.0118V17.16C18.0118 15.7963 17.4735 14.4867 16.5167 13.5219L5.40353 2.31433L2.29357 5.45069Z"
                fill="currentColor"
            />
        </svg>
    )
}

export function TavilyIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="42"
            height="42"
            viewBox="0 0 42 42"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="m16.44.964 4.921 7.79c.79 1.252-.108 2.883-1.588 2.883H17.76V23.3h-2.91V.088c.61 0 1.22.292 1.59.876z"
                fill="#8FBCFA"
            />
            <path
                d="M8.342 8.755 13.263.964a1.864 1.864 0 0 1 1.59-.876V23.3a4.87 4.87 0 0 0-.252-.006c-.99 0-1.907.311-2.658.842V11.637H9.93c-1.48 0-2.38-1.631-1.589-2.882z"
                fill="#468BFF"
            />
            <path
                d="M30.278 31H18.031a4.596 4.596 0 0 0 1.219-2.91h22.577c0 .61-.292 1.22-.875 1.59L33.16 34.6c-1.251.791-2.883-.108-2.883-1.588V31z"
                fill="#FDBB11"
            />
            <path
                d="m33.16 21.581 7.79 4.921c.585.369.876.979.876 1.589H19.25a4.619 4.619 0 0 0-.858-2.91h11.887V23.17c0-1.48 1.631-2.38 2.882-1.589z"
                fill="#F6D785"
            />
            <path
                d="m8.24 34.25-7.107 7.108a1.864 1.864 0 0 0 1.742.504l8.989-2.03c1.443-.325 1.961-2.114.915-3.16l-1.423-1.423 5.356-5.356a2.805 2.805 0 0 0 0-3.966l-.074-.075L8.24 34.25z"
                fill="#FF9A9D"
            />
            <path
                d="m7.243 31.135 5.355-5.356a2.805 2.805 0 0 1 3.967 0l.074.074-8.397 8.397-7.108 7.108a1.864 1.864 0 0 1-.504-1.742l2.029-8.989c.325-1.444 2.115-1.961 3.161-.915l1.423 1.423z"
                fill="#FE363B"
            />
        </svg>
    )
}

export function GroqIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            x="0px"
            y="0px"
            width="152px"
            height="55.5px"
            viewBox="0 32.25 36 55.5"
            fill="currentColor"
            {...props}
        >
            <path
                d="M17.77,34.048C7.971,34.048,0,42.019,0,51.817s7.971,17.77,17.77,17.77h5.844v-6.664H17.77
			c-6.124,0-11.106-4.982-11.106-11.106s4.982-11.106,11.106-11.106s11.132,4.982,11.132,11.106l0,0v16.365l0,0
			c0,6.084-4.954,11.039-11.023,11.103c-2.904-0.024-5.681-1.191-7.729-3.25l-4.712,4.712c3.266,3.283,7.691,5.151,12.321,5.201
			v0.003c0.04,0,0.08,0,0.119,0h0.125v-0.003c9.659-0.131,17.48-8.005,17.525-17.686l0.006-16.881
			C35.302,41.785,27.422,34.048,17.77,34.048z"
            />
        </svg>
    )
}

export function MetaIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="1em"
            height="1em"
            viewBox="0 0 24 24"
            {...props}
        >
            {/* Icon from Simple Icons by Simple Icons Collaborators - https://github.com/simple-icons/simple-icons/blob/develop/LICENSE.md */}
            <path
                fill="currentColor"
                d="M6.915 4.03c-1.968 0-3.683 1.28-4.871 3.113C.704 9.208 0 11.883 0 14.449c0 .706.07 1.369.21 1.973a7 7 0 0 0 .265.86a5.3 5.3 0 0 0 .371.761c.696 1.159 1.818 1.927 3.593 1.927c1.497 0 2.633-.671 3.965-2.444c.76-1.012 1.144-1.626 2.663-4.32l.756-1.339l.186-.325c.061.1.121.196.183.3l2.152 3.595c.724 1.21 1.665 2.556 2.47 3.314c1.046.987 1.992 1.22 3.06 1.22c1.075 0 1.876-.355 2.455-.843a3.7 3.7 0 0 0 .81-.973c.542-.939.861-2.127.861-3.745c0-2.72-.681-5.357-2.084-7.45c-1.282-1.912-2.957-2.93-4.716-2.93c-1.047 0-2.088.467-3.053 1.308c-.652.57-1.257 1.29-1.82 2.05c-.69-.875-1.335-1.547-1.958-2.056c-1.182-.966-2.315-1.303-3.454-1.303zm10.16 2.053c1.147 0 2.188.758 2.992 1.999c1.132 1.748 1.647 4.195 1.647 6.4c0 1.548-.368 2.9-1.839 2.9c-.58 0-1.027-.23-1.664-1.004c-.496-.601-1.343-1.878-2.832-4.358l-.617-1.028a45 45 0 0 0-1.255-1.98c.07-.109.141-.224.211-.327c1.12-1.667 2.118-2.602 3.358-2.602zm-10.201.553c1.265 0 2.058.791 2.675 1.446c.307.327.737.871 1.234 1.579l-1.02 1.566c-.757 1.163-1.882 3.017-2.837 4.338c-1.191 1.649-1.81 1.817-2.486 1.817c-.524 0-1.038-.237-1.383-.794c-.263-.426-.464-1.13-.464-2.046c0-2.221.63-4.535 1.66-6.088c.454-.687.964-1.226 1.533-1.533a2.26 2.26 0 0 1 1.088-.285"
            />
        </svg>
    )
}
