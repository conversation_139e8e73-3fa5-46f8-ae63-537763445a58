import { useThemeStore } from "@/lib/theme-store"
import { motion } from "motion/react"

export const <PERSON><PERSON><PERSON><PERSON> = ({ fill }: { fill: string }) => (
    <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M229.485 181.965C192.047 187.728 157.428 214.109 141.168 249.263C116.558 302.472 133.798 363.832 182.252 395.492C185.173 397.4 187.562 399.055 187.562 399.171C187.562 399.286 184.602 405.61 180.983 413.222C163.089 450.863 150.586 492.771 141.201 546.558C139.593 555.772 135.993 574.869 133.201 588.996C127.108 619.815 125.035 636.049 125.001 653.211C124.955 676.079 127.89 694.474 135.002 715.876C143.105 740.265 154.66 759.579 171.911 777.571C188.53 794.906 203.489 805.833 226.131 817.182C232.895 820.573 238.612 823.485 238.837 823.655C239.061 823.825 235.171 828.387 230.19 833.792C215.257 850.005 195.862 876.074 183.244 896.895L180.738 901.029L190.399 908.621C253.899 958.523 326.027 992.427 401.185 1007.7C484.803 1024.69 571.474 1020.58 648.814 995.958C691.499 982.368 735.555 961.081 774.484 935.237C786 927.591 809.282 910.336 816.304 904.243L820.673 900.453L812.883 888.191C800.53 868.748 782.287 845.242 767.966 830.314C763.354 825.507 762.085 823.559 763.304 823.162C768.037 821.626 787.625 810.576 797.961 803.612C812.886 793.555 834.993 771.73 844.537 757.63C857.632 738.28 866.634 716.871 871.769 692.857C874.821 678.589 875.992 645.55 874.05 628.566C873.317 622.159 870.008 602.844 866.695 585.646C863.384 568.447 858.851 544.827 856.623 533.157C851.491 506.292 845.315 482.25 837.646 459.299C831.616 441.254 819.237 411.883 814.232 403.747L811.568 399.416L820.695 392.69C832.139 384.257 843.208 372.972 849.898 362.917C884.181 311.405 871.313 241.663 820.4 203.039C804.517 190.99 781.892 182.729 760.714 181.245C717.93 178.248 674.903 203 656.609 241.135C654.419 245.698 652.628 250.075 652.628 250.86C652.628 252.805 652.344 252.738 645.049 249.075C622.023 237.513 582.474 226.887 546.416 222.574C524.017 219.894 479.182 219.885 456.987 222.555C420.753 226.915 386.442 235.737 358.136 247.975C352.649 250.346 348.02 252.287 347.849 252.287C347.676 252.287 345.562 248.081 343.151 242.941C325.558 205.436 290.851 182.722 249.049 181.353C241.978 181.122 233.174 181.398 229.485 181.965ZM368.669 400.896C413.294 408.126 446.012 459.13 439.181 510.821C437.018 527.193 437.062 527.147 433.075 517.292C426.935 502.121 418.629 492.415 407.404 487.293C401.58 484.636 399.861 484.364 391.753 484.811C384.65 485.203 381.302 485.999 376.551 488.427C358.915 497.442 346.31 522.114 346.31 547.619C346.31 565.841 351.737 579.815 363.918 592.957C371.916 601.587 383.871 608.51 390.898 608.58L394.941 608.621L388.607 614.587C360.929 640.655 316.753 638.877 286.357 610.473C266.726 592.129 256.572 565.923 256.615 533.715C256.64 514.923 258.991 500.27 264.733 483.103C283.493 427.014 325.381 393.883 368.669 400.896ZM662.394 402.567C699.833 412.067 732.61 455.485 741.484 507.338C743.909 521.508 743.708 547.22 741.071 559.98C733.357 597.319 706.992 625.031 673.002 631.527C662.459 633.542 656.107 633.522 645.18 631.436C633.143 629.139 619.989 622.621 611.764 614.881L605.115 608.621L609.716 608.58C621.583 608.475 639.819 594.054 646.945 579.139C652.868 566.741 654.296 559.472 654.222 542.091C654.161 528.206 653.83 525.548 651.26 518.342C645.892 503.291 637.808 493.635 625.931 488.086C620.903 485.736 617.969 485.149 611.24 485.143C599.695 485.133 590.773 489.479 581.071 499.84C574.558 506.794 565.545 522.348 565.357 526.956C565.199 530.832 562.947 522.415 561.339 511.938C553.386 460.102 586.243 407.92 631.305 400.824C639.122 399.594 653.95 400.426 662.394 402.567ZM513.94 610.277C527.384 613.332 536.348 621.072 536.357 629.636C536.367 638.296 526.654 650.739 514.349 657.83C502.187 664.839 489.919 662.499 476.371 650.585C466.269 641.702 461.935 628.806 466.734 621.911C474.477 610.787 494.484 605.856 513.94 610.277ZM375.738 685.384C396.892 705.567 415.57 713.911 439.659 713.942C458.131 713.967 472.891 709.25 486.528 698.964C498.435 689.984 503.222 689.941 514.723 698.705C535.563 714.586 562.417 718.573 588.201 709.615C599.656 705.636 612.402 697.435 624.227 686.435C634.236 677.126 634.973 676.663 639.81 676.663C644.15 676.663 645.529 677.272 649.292 680.851C658.439 689.55 657.762 700.279 647.114 715.427C629.476 740.514 592.993 756.549 558.244 754.483C538.204 753.291 522.515 747.832 503.941 735.586C501.225 733.795 500.925 733.884 493.32 738.708C471.961 752.257 444.151 757.614 418.427 753.133C389.028 748.012 359.75 728.884 349.266 707.951C344.524 698.48 344.086 693.054 347.495 686.017C350.413 679.997 355.442 676.707 361.764 676.682C366.279 676.664 367.198 677.236 375.738 685.384Z"
        fill={fill}
    />
)

export const LogoSymbol = ({ className }: { className?: string }) => {
    return (
        <svg
            viewBox="0 0 1000 1000"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
        >
            <LogoPath fill={"currentColor"} />
        </svg>
    )
}

export function Logo() {
    const { themeState } = useThemeStore()
    const mode = themeState.currentMode
    const styles = themeState.cssVars[mode]

    const logoBg = mode === "dark" ? styles.foreground : styles.background
    const logoFg = mode === "dark" ? styles.background : styles.foreground

    return (
        <svg viewBox="0 0 1000 1000" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1462_364)">
                <rect width="1000" height="1000" rx="500" fill={logoBg} />
                <LogoPath fill={logoFg} />

                {/* Animated eyelids for blinking effect */}
                <motion.g
                    initial={{ scaleY: 0, y: 0 }}
                    animate={{ scaleY: [0, 0, 1, 0, 0], y: 30 }}
                    transition={{
                        duration: 0.8,
                        times: [0, 0.15, 0.25, 0.35, 0.85],
                        repeat: Number.POSITIVE_INFINITY,
                        repeatDelay: 4
                    }}
                    style={{ transformOrigin: "center" }}
                >
                    {/* Left eyelid */}
                    <circle cx="368" cy="400" r="200" fill={logoFg} />
                    {/* Right eyelid */}
                    <circle cx="632" cy="400" r="200" fill={logoFg} />
                </motion.g>
            </g>
            <defs>
                <clipPath id="clip0_1462_364">
                    <rect width="1000" height="1000" rx="500" fill={logoBg} />
                </clipPath>
            </defs>
        </svg>
    )
}

export function LogoMark({ className }: { className?: string }) {
    return (
        <svg
            width="958"
            height="117"
            viewBox="0 0 958 117"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
        >
            <g>
                <path
                    d="M25.2583 21.5538H0V1.68389H25.2583V21.5538ZM25.2583 114.504H0V30.1416H25.2583V114.504Z"
                    fill="currentColor"
                />
                <path
                    d="M57.0023 114.504H31.7439V30.1416H55.15V56.0735H56.6655C58.8546 41.9288 68.9579 28.4577 91.0168 28.4577C114.086 28.4577 125.031 43.2759 125.031 61.9671V114.504H99.7731V70.3866C99.7731 56.9154 94.2162 51.1902 78.7244 51.1902C62.7275 51.1902 57.0023 57.589 57.0023 71.9021V114.504Z"
                    fill="currentColor"
                />
                <path
                    d="M190.515 114.504H169.635C149.933 114.504 137.978 105.243 137.978 84.0261V51.0218H124.675V30.1416H137.978V13.8079H163.236V30.1416H190.515V51.0218H163.236V81.1635C163.236 89.5829 166.435 91.7719 175.36 91.7719H190.515V114.504Z"
                    fill="currentColor"
                />
                <path
                    d="M238.031 116.188C209.91 116.188 190.714 102.38 190.714 72.4072C190.714 45.465 209.742 28.4577 237.526 28.4577C265.141 28.4577 283.496 42.9392 283.496 69.3762C283.496 72.4072 283.159 74.5963 282.822 77.4589H214.12C214.793 90.4248 220.855 95.9817 237.189 95.9817C252.176 95.9817 257.396 92.1087 257.396 84.868V83.1841H282.654V85.0364C282.654 103.391 264.805 116.188 238.031 116.188ZM237.021 48.1592C222.034 48.1592 215.635 53.2109 214.456 64.1562H259.248C258.574 53.0425 251.839 48.1592 237.021 48.1592Z"
                    fill="currentColor"
                />
                <path
                    d="M312.73 114.504H287.471V30.1416H310.878V52.5373H312.393C314.75 38.8978 323.675 28.4577 340.682 28.4577C359.542 28.4577 367.288 41.4237 367.288 57.9258V71.9021H342.029V63.1458C342.029 53.8844 338.325 49.8431 328.053 49.8431C316.771 49.8431 312.73 55.0632 312.73 65.6717V114.504Z"
                    fill="currentColor"
                />
                <path
                    d="M394.957 114.504H369.699V30.1416H393.105V56.0735H394.621C396.81 41.9288 406.913 28.4577 428.972 28.4577C452.041 28.4577 462.987 43.2759 462.987 61.9671V114.504H437.728V70.3866C437.728 56.9154 432.171 51.1902 416.68 51.1902C400.683 51.1902 394.957 57.589 394.957 71.9021V114.504Z"
                    fill="currentColor"
                />
                <path
                    d="M520.051 116.188C483.174 116.188 466.503 101.202 466.503 76.2802V73.5859H493.277V76.2802C493.277 87.7306 496.476 91.9403 518.872 91.9403C540.089 91.9403 546.656 88.5726 546.656 78.806C546.656 70.3866 541.268 66.8504 530.659 66.8504H498.16V46.1386H528.302C538.068 46.1386 542.446 42.6024 542.446 35.5301C542.446 27.6158 537.732 24.248 518.535 24.248C498.329 24.248 493.277 28.4577 493.277 39.9082V40.7501H466.503V39.9082C466.503 16.1653 484.015 0 520.724 0C550.866 0 567.536 10.2717 567.536 30.1416C567.536 43.1076 559.79 51.8638 545.141 53.7161V55.5683C561.643 57.9258 572.42 66.3452 572.42 82.8473C572.42 104.738 552.887 116.188 520.051 116.188Z"
                    fill="currentColor"
                />
                <path d="M606.08 114.504H573.581V85.0364H606.08V114.504Z" fill="currentColor" />
                <path
                    d="M656.08 116.188C627.117 116.188 608.594 99.0127 608.594 72.4072C608.594 45.465 627.117 28.4577 656.08 28.4577C683.191 28.4577 701.713 42.9392 701.713 64.8297V67.1872H676.623V65.8401C676.623 55.0632 668.709 51.1902 655.575 51.1902C640.588 51.1902 633.684 56.7471 633.684 72.4072C633.684 87.899 640.588 93.4558 655.575 93.4558C668.709 93.4558 676.623 89.5829 676.623 78.806V77.4589H701.713V79.8163C701.713 101.539 683.191 116.188 656.08 116.188Z"
                    fill="currentColor"
                />
                <path
                    d="M731.104 114.504H705.846V1.68389H731.104V55.9051H732.451C735.145 41.5921 745.08 28.4577 766.634 28.4577C789.366 28.4577 800.48 43.2759 800.48 62.8091V114.504H775.222V70.3866C775.222 56.2419 769.16 51.1902 753.331 51.1902C736.324 51.1902 731.104 58.0942 731.104 72.2388V114.504Z"
                    fill="currentColor"
                />
                <path
                    d="M832.423 116.188C814.237 116.188 803.291 107.769 803.291 93.6242C803.291 81.1634 812.553 73.4176 830.57 71.5653L866.774 67.8607V64.6613C866.774 53.7161 861.891 50.6851 848.42 50.6851C835.622 50.6851 830.402 54.0528 830.402 63.651V64.3246H804.975V63.8194C804.975 42.9392 822.488 28.4577 850.272 28.4577C878.393 28.4577 891.696 42.9392 891.696 64.9981V114.504H868.121V95.1397H866.774C862.901 108.274 851.114 116.188 832.423 116.188ZM828.718 91.9403C828.718 96.4868 832.254 98.3391 839.663 98.3391C856.839 98.3391 866.1 94.2978 866.774 82.3422L837.474 85.71C831.412 86.2151 828.718 87.899 828.718 91.9403Z"
                    fill="currentColor"
                />
                <path
                    d="M957.204 114.504H936.324C916.622 114.504 904.667 105.243 904.667 84.0261V51.0218H891.364V30.1416H904.667V13.8079H929.925V30.1416H957.204V51.0218H929.925V81.1635C929.925 89.5829 933.124 91.7719 942.049 91.7719H957.204V114.504Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    )
}
