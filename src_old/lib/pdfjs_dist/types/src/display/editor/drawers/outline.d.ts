export class Outline {
    static PRECISION: number;
    static _rescale(src: any, tx: any, ty: any, sx: any, sy: any, dest: any): any;
    static _rescaleAndSwap(src: any, tx: any, ty: any, sx: any, sy: any, dest: any): any;
    static _translate(src: any, tx: any, ty: any, dest: any): any;
    static svgRound(x: any): number;
    static _normalizePoint(x: any, y: any, parentWidth: any, parentHeight: any, rotation: any): number[];
    static _normalizePagePoint(x: any, y: any, rotation: any): any[];
    static createBezierPoints(x1: any, y1: any, x2: any, y2: any, x3: any, y3: any): number[];
    /**
     * @returns {string} The SVG path of the outline.
     */
    toSVGPath(): string;
    /**
     * @type {Object|null} The bounding box of the outline.
     */
    get box(): Object | null;
    serialize(_bbox: any, _rotation: any): void;
}
