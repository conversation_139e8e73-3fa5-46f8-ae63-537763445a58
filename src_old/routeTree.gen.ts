/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'
import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as ChatRouteImport } from './routes/_chat'
import { Route as ChatIndexRouteImport } from './routes/_chat.index'
import { Route as SettingsUsageRouteImport } from './routes/settings/usage'
import { Route as SettingsProvidersRouteImport } from './routes/settings/providers'
import { Route as SettingsProfileRouteImport } from './routes/settings/profile'
import { Route as SettingsModelsRouteImport } from './routes/settings/models'
import { Route as SettingsCustomizationRouteImport } from './routes/settings/customization'
import { Route as SettingsAttachmentsRouteImport } from './routes/settings/attachments'
import { Route as SettingsAppearanceRouteImport } from './routes/settings/appearance'
import { Route as SettingsAiOptionsRouteImport } from './routes/settings/ai-options'
import { Route as ChatLibraryRouteImport } from './routes/_chat.library'
import { Route as ChatThreadThreadIdRouteImport } from './routes/_chat.thread.$threadId'
import { ServerRoute as ApiPhrSplatServerRouteImport } from './routes/api/phr/$'
import { ServerRoute as ApiAuthSplatServerRouteImport } from './routes/api/auth/$'

const PrivacyPolicyLazyRouteImport = createFileRoute('/privacy-policy')()
const AboutLazyRouteImport = createFileRoute('/about')()
const SettingsRouteLazyRouteImport = createFileRoute('/settings')()
const AuthPathnameLazyRouteImport = createFileRoute('/auth/$pathname')()
const ChatSSharedThreadIdLazyRouteImport = createFileRoute(
  '/_chat/s/$sharedThreadId',
)()
const ChatFolderFolderIdLazyRouteImport = createFileRoute(
  '/_chat/folder/$folderId',
)()
const rootServerRouteImport = createServerRootRoute()

const PrivacyPolicyLazyRoute = PrivacyPolicyLazyRouteImport.update({
  id: '/privacy-policy',
  path: '/privacy-policy',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/privacy-policy.lazy').then((d) => d.Route),
)
const AboutLazyRoute = AboutLazyRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/about.lazy').then((d) => d.Route))
const SettingsRouteLazyRoute = SettingsRouteLazyRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/settings/route.lazy').then((d) => d.Route),
)
const ChatRoute = ChatRouteImport.update({
  id: '/_chat',
  getParentRoute: () => rootRouteImport,
} as any)
const ChatIndexRoute = ChatIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => ChatRoute,
} as any)
const AuthPathnameLazyRoute = AuthPathnameLazyRouteImport.update({
  id: '/auth/$pathname',
  path: '/auth/$pathname',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/auth/$pathname.lazy').then((d) => d.Route),
)
const SettingsUsageRoute = SettingsUsageRouteImport.update({
  id: '/usage',
  path: '/usage',
  getParentRoute: () => SettingsRouteLazyRoute,
} as any)
const SettingsProvidersRoute = SettingsProvidersRouteImport.update({
  id: '/providers',
  path: '/providers',
  getParentRoute: () => SettingsRouteLazyRoute,
} as any)
const SettingsProfileRoute = SettingsProfileRouteImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => SettingsRouteLazyRoute,
} as any)
const SettingsModelsRoute = SettingsModelsRouteImport.update({
  id: '/models',
  path: '/models',
  getParentRoute: () => SettingsRouteLazyRoute,
} as any)
const SettingsCustomizationRoute = SettingsCustomizationRouteImport.update({
  id: '/customization',
  path: '/customization',
  getParentRoute: () => SettingsRouteLazyRoute,
} as any)
const SettingsAttachmentsRoute = SettingsAttachmentsRouteImport.update({
  id: '/attachments',
  path: '/attachments',
  getParentRoute: () => SettingsRouteLazyRoute,
} as any)
const SettingsAppearanceRoute = SettingsAppearanceRouteImport.update({
  id: '/appearance',
  path: '/appearance',
  getParentRoute: () => SettingsRouteLazyRoute,
} as any)
const SettingsAiOptionsRoute = SettingsAiOptionsRouteImport.update({
  id: '/ai-options',
  path: '/ai-options',
  getParentRoute: () => SettingsRouteLazyRoute,
} as any)
const ChatLibraryRoute = ChatLibraryRouteImport.update({
  id: '/library',
  path: '/library',
  getParentRoute: () => ChatRoute,
} as any)
const ChatSSharedThreadIdLazyRoute = ChatSSharedThreadIdLazyRouteImport.update({
  id: '/s/$sharedThreadId',
  path: '/s/$sharedThreadId',
  getParentRoute: () => ChatRoute,
} as any).lazy(() =>
  import('./routes/_chat.s.$sharedThreadId.lazy').then((d) => d.Route),
)
const ChatFolderFolderIdLazyRoute = ChatFolderFolderIdLazyRouteImport.update({
  id: '/folder/$folderId',
  path: '/folder/$folderId',
  getParentRoute: () => ChatRoute,
} as any).lazy(() =>
  import('./routes/_chat.folder.$folderId.lazy').then((d) => d.Route),
)
const ChatThreadThreadIdRoute = ChatThreadThreadIdRouteImport.update({
  id: '/thread/$threadId',
  path: '/thread/$threadId',
  getParentRoute: () => ChatRoute,
} as any)
const ApiPhrSplatServerRoute = ApiPhrSplatServerRouteImport.update({
  id: '/api/phr/$',
  path: '/api/phr/$',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiAuthSplatServerRoute = ApiAuthSplatServerRouteImport.update({
  id: '/api/auth/$',
  path: '/api/auth/$',
  getParentRoute: () => rootServerRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/settings': typeof SettingsRouteLazyRouteWithChildren
  '/about': typeof AboutLazyRoute
  '/privacy-policy': typeof PrivacyPolicyLazyRoute
  '/library': typeof ChatLibraryRoute
  '/settings/ai-options': typeof SettingsAiOptionsRoute
  '/settings/appearance': typeof SettingsAppearanceRoute
  '/settings/attachments': typeof SettingsAttachmentsRoute
  '/settings/customization': typeof SettingsCustomizationRoute
  '/settings/models': typeof SettingsModelsRoute
  '/settings/profile': typeof SettingsProfileRoute
  '/settings/providers': typeof SettingsProvidersRoute
  '/settings/usage': typeof SettingsUsageRoute
  '/auth/$pathname': typeof AuthPathnameLazyRoute
  '/': typeof ChatIndexRoute
  '/thread/$threadId': typeof ChatThreadThreadIdRoute
  '/folder/$folderId': typeof ChatFolderFolderIdLazyRoute
  '/s/$sharedThreadId': typeof ChatSSharedThreadIdLazyRoute
}
export interface FileRoutesByTo {
  '/settings': typeof SettingsRouteLazyRouteWithChildren
  '/about': typeof AboutLazyRoute
  '/privacy-policy': typeof PrivacyPolicyLazyRoute
  '/library': typeof ChatLibraryRoute
  '/settings/ai-options': typeof SettingsAiOptionsRoute
  '/settings/appearance': typeof SettingsAppearanceRoute
  '/settings/attachments': typeof SettingsAttachmentsRoute
  '/settings/customization': typeof SettingsCustomizationRoute
  '/settings/models': typeof SettingsModelsRoute
  '/settings/profile': typeof SettingsProfileRoute
  '/settings/providers': typeof SettingsProvidersRoute
  '/settings/usage': typeof SettingsUsageRoute
  '/auth/$pathname': typeof AuthPathnameLazyRoute
  '/': typeof ChatIndexRoute
  '/thread/$threadId': typeof ChatThreadThreadIdRoute
  '/folder/$folderId': typeof ChatFolderFolderIdLazyRoute
  '/s/$sharedThreadId': typeof ChatSSharedThreadIdLazyRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_chat': typeof ChatRouteWithChildren
  '/settings': typeof SettingsRouteLazyRouteWithChildren
  '/about': typeof AboutLazyRoute
  '/privacy-policy': typeof PrivacyPolicyLazyRoute
  '/_chat/library': typeof ChatLibraryRoute
  '/settings/ai-options': typeof SettingsAiOptionsRoute
  '/settings/appearance': typeof SettingsAppearanceRoute
  '/settings/attachments': typeof SettingsAttachmentsRoute
  '/settings/customization': typeof SettingsCustomizationRoute
  '/settings/models': typeof SettingsModelsRoute
  '/settings/profile': typeof SettingsProfileRoute
  '/settings/providers': typeof SettingsProvidersRoute
  '/settings/usage': typeof SettingsUsageRoute
  '/auth/$pathname': typeof AuthPathnameLazyRoute
  '/_chat/': typeof ChatIndexRoute
  '/_chat/thread/$threadId': typeof ChatThreadThreadIdRoute
  '/_chat/folder/$folderId': typeof ChatFolderFolderIdLazyRoute
  '/_chat/s/$sharedThreadId': typeof ChatSSharedThreadIdLazyRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/settings'
    | '/about'
    | '/privacy-policy'
    | '/library'
    | '/settings/ai-options'
    | '/settings/appearance'
    | '/settings/attachments'
    | '/settings/customization'
    | '/settings/models'
    | '/settings/profile'
    | '/settings/providers'
    | '/settings/usage'
    | '/auth/$pathname'
    | '/'
    | '/thread/$threadId'
    | '/folder/$folderId'
    | '/s/$sharedThreadId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/settings'
    | '/about'
    | '/privacy-policy'
    | '/library'
    | '/settings/ai-options'
    | '/settings/appearance'
    | '/settings/attachments'
    | '/settings/customization'
    | '/settings/models'
    | '/settings/profile'
    | '/settings/providers'
    | '/settings/usage'
    | '/auth/$pathname'
    | '/'
    | '/thread/$threadId'
    | '/folder/$folderId'
    | '/s/$sharedThreadId'
  id:
    | '__root__'
    | '/_chat'
    | '/settings'
    | '/about'
    | '/privacy-policy'
    | '/_chat/library'
    | '/settings/ai-options'
    | '/settings/appearance'
    | '/settings/attachments'
    | '/settings/customization'
    | '/settings/models'
    | '/settings/profile'
    | '/settings/providers'
    | '/settings/usage'
    | '/auth/$pathname'
    | '/_chat/'
    | '/_chat/thread/$threadId'
    | '/_chat/folder/$folderId'
    | '/_chat/s/$sharedThreadId'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  ChatRoute: typeof ChatRouteWithChildren
  SettingsRouteLazyRoute: typeof SettingsRouteLazyRouteWithChildren
  AboutLazyRoute: typeof AboutLazyRoute
  PrivacyPolicyLazyRoute: typeof PrivacyPolicyLazyRoute
  AuthPathnameLazyRoute: typeof AuthPathnameLazyRoute
}
export interface FileServerRoutesByFullPath {
  '/api/auth/$': typeof ApiAuthSplatServerRoute
  '/api/phr/$': typeof ApiPhrSplatServerRoute
}
export interface FileServerRoutesByTo {
  '/api/auth/$': typeof ApiAuthSplatServerRoute
  '/api/phr/$': typeof ApiPhrSplatServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/api/auth/$': typeof ApiAuthSplatServerRoute
  '/api/phr/$': typeof ApiPhrSplatServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths: '/api/auth/$' | '/api/phr/$'
  fileServerRoutesByTo: FileServerRoutesByTo
  to: '/api/auth/$' | '/api/phr/$'
  id: '__root__' | '/api/auth/$' | '/api/phr/$'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  ApiAuthSplatServerRoute: typeof ApiAuthSplatServerRoute
  ApiPhrSplatServerRoute: typeof ApiPhrSplatServerRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/privacy-policy': {
      id: '/privacy-policy'
      path: '/privacy-policy'
      fullPath: '/privacy-policy'
      preLoaderRoute: typeof PrivacyPolicyLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsRouteLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_chat': {
      id: '/_chat'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof ChatRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_chat/': {
      id: '/_chat/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof ChatIndexRouteImport
      parentRoute: typeof ChatRoute
    }
    '/auth/$pathname': {
      id: '/auth/$pathname'
      path: '/auth/$pathname'
      fullPath: '/auth/$pathname'
      preLoaderRoute: typeof AuthPathnameLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings/usage': {
      id: '/settings/usage'
      path: '/usage'
      fullPath: '/settings/usage'
      preLoaderRoute: typeof SettingsUsageRouteImport
      parentRoute: typeof SettingsRouteLazyRoute
    }
    '/settings/providers': {
      id: '/settings/providers'
      path: '/providers'
      fullPath: '/settings/providers'
      preLoaderRoute: typeof SettingsProvidersRouteImport
      parentRoute: typeof SettingsRouteLazyRoute
    }
    '/settings/profile': {
      id: '/settings/profile'
      path: '/profile'
      fullPath: '/settings/profile'
      preLoaderRoute: typeof SettingsProfileRouteImport
      parentRoute: typeof SettingsRouteLazyRoute
    }
    '/settings/models': {
      id: '/settings/models'
      path: '/models'
      fullPath: '/settings/models'
      preLoaderRoute: typeof SettingsModelsRouteImport
      parentRoute: typeof SettingsRouteLazyRoute
    }
    '/settings/customization': {
      id: '/settings/customization'
      path: '/customization'
      fullPath: '/settings/customization'
      preLoaderRoute: typeof SettingsCustomizationRouteImport
      parentRoute: typeof SettingsRouteLazyRoute
    }
    '/settings/attachments': {
      id: '/settings/attachments'
      path: '/attachments'
      fullPath: '/settings/attachments'
      preLoaderRoute: typeof SettingsAttachmentsRouteImport
      parentRoute: typeof SettingsRouteLazyRoute
    }
    '/settings/appearance': {
      id: '/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof SettingsAppearanceRouteImport
      parentRoute: typeof SettingsRouteLazyRoute
    }
    '/settings/ai-options': {
      id: '/settings/ai-options'
      path: '/ai-options'
      fullPath: '/settings/ai-options'
      preLoaderRoute: typeof SettingsAiOptionsRouteImport
      parentRoute: typeof SettingsRouteLazyRoute
    }
    '/_chat/library': {
      id: '/_chat/library'
      path: '/library'
      fullPath: '/library'
      preLoaderRoute: typeof ChatLibraryRouteImport
      parentRoute: typeof ChatRoute
    }
    '/_chat/s/$sharedThreadId': {
      id: '/_chat/s/$sharedThreadId'
      path: '/s/$sharedThreadId'
      fullPath: '/s/$sharedThreadId'
      preLoaderRoute: typeof ChatSSharedThreadIdLazyRouteImport
      parentRoute: typeof ChatRoute
    }
    '/_chat/folder/$folderId': {
      id: '/_chat/folder/$folderId'
      path: '/folder/$folderId'
      fullPath: '/folder/$folderId'
      preLoaderRoute: typeof ChatFolderFolderIdLazyRouteImport
      parentRoute: typeof ChatRoute
    }
    '/_chat/thread/$threadId': {
      id: '/_chat/thread/$threadId'
      path: '/thread/$threadId'
      fullPath: '/thread/$threadId'
      preLoaderRoute: typeof ChatThreadThreadIdRouteImport
      parentRoute: typeof ChatRoute
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/api/phr/$': {
      id: '/api/phr/$'
      path: '/api/phr/$'
      fullPath: '/api/phr/$'
      preLoaderRoute: typeof ApiPhrSplatServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/auth/$': {
      id: '/api/auth/$'
      path: '/api/auth/$'
      fullPath: '/api/auth/$'
      preLoaderRoute: typeof ApiAuthSplatServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
  }
}

interface ChatRouteChildren {
  ChatLibraryRoute: typeof ChatLibraryRoute
  ChatIndexRoute: typeof ChatIndexRoute
  ChatThreadThreadIdRoute: typeof ChatThreadThreadIdRoute
  ChatFolderFolderIdLazyRoute: typeof ChatFolderFolderIdLazyRoute
  ChatSSharedThreadIdLazyRoute: typeof ChatSSharedThreadIdLazyRoute
}

const ChatRouteChildren: ChatRouteChildren = {
  ChatLibraryRoute: ChatLibraryRoute,
  ChatIndexRoute: ChatIndexRoute,
  ChatThreadThreadIdRoute: ChatThreadThreadIdRoute,
  ChatFolderFolderIdLazyRoute: ChatFolderFolderIdLazyRoute,
  ChatSSharedThreadIdLazyRoute: ChatSSharedThreadIdLazyRoute,
}

const ChatRouteWithChildren = ChatRoute._addFileChildren(ChatRouteChildren)

interface SettingsRouteLazyRouteChildren {
  SettingsAiOptionsRoute: typeof SettingsAiOptionsRoute
  SettingsAppearanceRoute: typeof SettingsAppearanceRoute
  SettingsAttachmentsRoute: typeof SettingsAttachmentsRoute
  SettingsCustomizationRoute: typeof SettingsCustomizationRoute
  SettingsModelsRoute: typeof SettingsModelsRoute
  SettingsProfileRoute: typeof SettingsProfileRoute
  SettingsProvidersRoute: typeof SettingsProvidersRoute
  SettingsUsageRoute: typeof SettingsUsageRoute
}

const SettingsRouteLazyRouteChildren: SettingsRouteLazyRouteChildren = {
  SettingsAiOptionsRoute: SettingsAiOptionsRoute,
  SettingsAppearanceRoute: SettingsAppearanceRoute,
  SettingsAttachmentsRoute: SettingsAttachmentsRoute,
  SettingsCustomizationRoute: SettingsCustomizationRoute,
  SettingsModelsRoute: SettingsModelsRoute,
  SettingsProfileRoute: SettingsProfileRoute,
  SettingsProvidersRoute: SettingsProvidersRoute,
  SettingsUsageRoute: SettingsUsageRoute,
}

const SettingsRouteLazyRouteWithChildren =
  SettingsRouteLazyRoute._addFileChildren(SettingsRouteLazyRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  ChatRoute: ChatRouteWithChildren,
  SettingsRouteLazyRoute: SettingsRouteLazyRouteWithChildren,
  AboutLazyRoute: AboutLazyRoute,
  PrivacyPolicyLazyRoute: PrivacyPolicyLazyRoute,
  AuthPathnameLazyRoute: AuthPathnameLazyRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiAuthSplatServerRoute: ApiAuthSplatServerRoute,
  ApiPhrSplatServerRoute: ApiPhrSplatServerRoute,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
