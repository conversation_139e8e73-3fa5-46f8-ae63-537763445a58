@plugin "tailwindcss-safe-area";
@plugin "@tailwindcss/typography";

@source "../node_modules/@daveyplate/better-auth-ui";

@layer base {
    button:not(:disabled),
    [role="button"]:not(:disabled) {
        cursor: pointer;
    }
}

[role="menuitem"]:not(:disabled) {
    cursor: pointer;
}

:root {
    --warning: hsl(38 92% 50%);
    --warning-foreground: hsl(48 96% 89%);
}

.dark {
    --warning: hsl(48 96% 89%);
    --warning-foreground: hsl(38 92% 50%);
}

@theme inline {
    --color-warning: var(--warning);
    --color-warning-foreground: var(--warning-foreground);
}

/** iOS Dynamic System Font Scaling */
/* @supports (-webkit-touch-callout: none) {
    html {
        font: -apple-system-body;
    }
} */

/** Sandpack Styling */
.sandpack-container {
    .sp-wrapper,
    .sp-layout,
    .sp-preview,
    .sp-preview-container,
    iframe {
        width: 100% !important;
        height: 100% !important;
        border-radius: 0 !important;
        border: none !important;
    }
    
    .sp-layout {
        border: 1px solid hsl(var(--border)) !important;
        border-radius: 0.5rem !important;
        overflow: hidden;
    }
}
