@import "tailwindcss";
@import "tw-animate-css";
@import "./custom.css";

@custom-variant dark (&:is(.dark *));

:root {
    --background: oklch(0.9711 0.0074 80.7211);
    --foreground: oklch(0.3 0.0358 30.2042);
    --card: oklch(0.9711 0.0074 80.7211);
    --card-foreground: oklch(0.3 0.0358 30.2042);
    --popover: oklch(0.9711 0.0074 80.7211);
    --popover-foreground: oklch(0.3 0.0358 30.2042);
    --primary: oklch(0.5234 0.1347 144.1672);
    --primary-foreground: oklch(1.0 0 0);
    --secondary: oklch(0.9571 0.021 147.636);
    --secondary-foreground: oklch(0.4254 0.1159 144.3078);
    --muted: oklch(0.937 0.0142 74.4218);
    --muted-foreground: oklch(0.4495 0.0486 39.211);
    --accent: oklch(0.8952 0.0504 146.0366);
    --accent-foreground: oklch(0.4254 0.1159 144.3078);
    --destructive: oklch(0.5386 0.1937 26.7249);
    --destructive-foreground: oklch(1.0 0 0);
    --border: oklch(0.8805 0.0208 74.6428);
    --input: oklch(0.8805 0.0208 74.6428);
    --ring: oklch(0.5234 0.1347 144.1672);
    --chart-1: oklch(0.6731 0.1624 144.2083);
    --chart-2: oklch(0.5752 0.1446 144.1813);
    --chart-3: oklch(0.5234 0.1347 144.1672);
    --chart-4: oklch(0.4254 0.1159 144.3078);
    --chart-5: oklch(0.2157 0.0453 145.7256);
    --sidebar: oklch(0.937 0.0142 74.4218);
    --sidebar-foreground: oklch(0.3 0.0358 30.2042);
    --sidebar-primary: oklch(0.5234 0.1347 144.1672);
    --sidebar-primary-foreground: oklch(1.0 0 0);
    --sidebar-accent: oklch(0.8952 0.0504 146.0366);
    --sidebar-accent-foreground: oklch(0.4254 0.1159 144.3078);
    --sidebar-border: oklch(0.8805 0.0208 74.6428);
    --sidebar-ring: oklch(0.5234 0.1347 144.1672);
    --font-sans: Montserrat, sans-serif;
    --font-serif: Merriweather, serif;
    --font-mono: Source Code Pro, monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
    --background: oklch(0 0 0);
    --foreground: oklch(0.9288 0.0126 255.5078);
    --card: oklch(0.1684 0 0);
    --card-foreground: oklch(0.9288 0.0126 255.5078);
    --popover: oklch(0.2603 0 0);
    --popover-foreground: oklch(0.7348 0 0);
    --primary: oklch(0.4365 0.1044 156.7556);
    --primary-foreground: oklch(0.9213 0.0135 167.1556);
    --secondary: oklch(0.2603 0 0);
    --secondary-foreground: oklch(0.9851 0 0);
    --muted: oklch(0.2393 0 0);
    --muted-foreground: oklch(0.7122 0 0);
    --accent: oklch(0.3132 0 0);
    --accent-foreground: oklch(0.9851 0 0);
    --destructive: oklch(0.3123 0.0852 29.7877);
    --destructive-foreground: oklch(0.9368 0.0045 34.3092);
    --border: oklch(0.2264 0 0);
    --input: oklch(0.2603 0 0);
    --ring: oklch(0.8003 0.1821 151.711);
    --chart-1: oklch(0.8003 0.1821 151.711);
    --chart-2: oklch(0.7137 0.1434 254.624);
    --chart-3: oklch(0.709 0.1592 293.5412);
    --chart-4: oklch(0.8369 0.1644 84.4286);
    --chart-5: oklch(0.7845 0.1325 181.912);
    --sidebar: oklch(0.1684 0 0);
    --sidebar-foreground: oklch(0.6301 0 0);
    --sidebar-primary: oklch(0.4365 0.1044 156.7556);
    --sidebar-primary-foreground: oklch(0.9213 0.0135 167.1556);
    --sidebar-accent: oklch(0.3132 0 0);
    --sidebar-accent-foreground: oklch(0.9851 0 0);
    --sidebar-border: oklch(0.2809 0 0);
    --sidebar-ring: oklch(0.8003 0.1821 151.711);
    --font-sans: Outfit, sans-serif;
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
}

.prose {
    --tw-prose-body: var(--foreground);
    --tw-prose-headings: var(--foreground);
    --tw-prose-lead: var(--foreground);
    --tw-prose-links: var(--foreground);
    --tw-prose-bold: var(--foreground);
    --tw-prose-counters: var(--foreground);
    --tw-prose-bullets: var(--foreground);
    --tw-prose-hr: var(--foreground);
    --tw-prose-quotes: var(--muted-foreground);
    --tw-prose-quote-borders: var(--border);
    --tw-prose-captions: var(--foreground);
    --tw-prose-code: var(--foreground);
    --tw-prose-pre-code: var(--foreground);
    --tw-prose-pre-bg: var(--background);
    --tw-prose-th-borders: var(--border);
    --tw-prose-td-borders: var(--border);
}

/* View Transition Fade Effect */
::view-transition-old(root),
::view-transition-new(root) {
    animation-duration: 0.3s;
    animation-timing-function: ease-in-out;
}

::view-transition-old(root) {
    animation-name: fade-out;
}

::view-transition-new(root) {
    animation-name: fade-in;
}

@keyframes fade-out {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@keyframes typing {
    0%,
    100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    50% {
        transform: translateY(-2px);
        opacity: 1;
    }
}

@keyframes loading-dots {
    0%,
    100% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
}

@keyframes wave {
    0%,
    100% {
        transform: scaleY(1);
    }
    50% {
        transform: scaleY(0.6);
    }
}

@keyframes blink {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

@keyframes text-blink {
    0%,
    100% {
        color: var(--primary);
    }
    50% {
        color: var(--muted-foreground);
    }
}

@keyframes bounce-dots {
    0%,
    100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes thin-pulse {
    0%,
    100% {
        transform: scale(0.95);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.4;
    }
}

@keyframes pulse-dot {
    0%,
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.5);
        opacity: 1;
    }
}

@keyframes shimmer-text {
    0% {
        background-position: 150% center;
    }
    100% {
        background-position: -150% center;
    }
}

@keyframes wave-bars {
    0%,
    100% {
        transform: scaleY(1);
        opacity: 0.5;
    }
    50% {
        transform: scaleY(0.6);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% {
        background-position: 200% 50%;
    }
    100% {
        background-position: -200% 50%;
    }
}

@keyframes spinner-fade {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@layer base {
    * {
        @apply border-border outline-ring/50;
        --color-scrollbar-track: transparent;
        scrollbar-width: thin;
        scrollbar-color: var(--border) transparent;
    }
    body {
        @apply bg-background text-foreground;
    }

    ::selection {
        background-color: var(--accent);
        color: var(--accent-foreground);
    }

    ::-webkit-scrollbar {
        width: 0.5rem;
        scrollbar-width: thin;
    }

    ::-webkit-scrollbar-thumb {
        background-color: var(--border) !important;
        border-radius: 9999px;
    }

    ::-webkit-scrollbar-track {
        background: var(--color-scrollbar-track);
    }
}

@layer utilities {
    .scrollbar-thin {
        scrollbar-width: thin;
    }

    .scrollbar-hide {
        /* Firefox */
        scrollbar-width: none;

        /* WebKit browsers (Chrome, Safari, Edge) */
        &::-webkit-scrollbar {
            display: none;
        }
    }
}
